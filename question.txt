contentflow 配置的 redis 配置文件

{
    "host": "xme-envdev-redis.otyftu.clustercfg.apse1.cache.amazonaws.com",
    "port": 6379
}

content-api

xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com

old:
{
    "dsn": "envdev-user:k6xetf4YkB@tcp(xme-envdev-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com:3306)/contentflow?charset=utf8mb4&parseTime=True&loc=Local"
}



{
    "dsn": "envtest-user:k7xetf5YKB@tcp(xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com:3306)/contentflow?charset=utf8mb4&parseTime=True&loc=Local"
}

xme-envtest-rds.chkycqw22fzd.ap-southeast-1.rds.amazonaws.com



2025-03-30 18:47:36     You need to specify one of the following as an environment variable:
2025-03-30 18:47:36     - MYSQL_ROOT_PASSWORD
2025-03-30 18:47:36     - MYSQL_ALLOW_EMPTY_PASSWORD
2025-03-30 18:47:36     - MYSQL_RANDOM_ROOT_PASSWORD


姓名： 王大明
英文名： Eddie
居民身份证号码： 130281199310050011
家庭住址： 北京市昌平区龙锦苑东四区
联系电话： 18515803091
邮箱地址： <EMAIL>
Polygon地址: 0x3c7b4d4b51c323187b652a3ce5234861660094d9


export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890



curl  'http://envtest-api.x.me/geo/api/ip?ip=**************'


Last login: Wed Apr  2 14:18:42 on ttys003
~ %
~ %
~ % ls
Applications		Library			Work
DataGripProjects	Movies			ai_completion
Desktop			Music			go
Documents		Pictures
Downloads		Public
~ %
~ %
~ %
~ %
~ %
~ %
~ %
~ %
~ %
~ % z /Users/<USER>/Work/company/xme/project/content-api
content-api %
content-api %
content-api %
content-api % ls
Dockerfile		api-test		content.md		docker-compose.yml	env_local      		go.mod			internal
README.md		cmd			db-init			docs			es_search.md   		go.sum			scripts
content-api %
content-api %
content-api % z /Users/<USER>/Work
Work %
Work %
Work % ls
company		go		mine		my_github	tools		tools_config
Work %
Work %
Work %
Work % z Work
zoxide: you are already in the only match
Work %
Work %
Work %
Work % z content-api
content-api %
content-api % z Wo
Work %
Session Contents Restored on Apr 7, 2025 at 08:15

Last login: Mon Apr  7 08:14:59 on console
content-api %
content-api %
content-api % curl  'http://envtest-api.x.me/geo/api/ip?ip=**************'
{"code":200,"message":"success","success":true,"result":{"ip":"**************","country":"China","country_code":"CN"},"request_id":"39edc3b6-3117-48ab-ac2c-183954c1b2e6"}%



curl 'http://envtest-api.x.me/geo/api/ip?ip=127.0.0.1'


content-api %
content-api %
content-api %
content-api %
content-api %
content-api % curl --request POST \
  --url http://cdn-api.x.me/user/api/user/login \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-al


// 登录获取 token
curl --request POST \
  --url http://cdn-api.x.me/user/api/user/login \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
	"email": "<EMAIL>",
	"password": "19930901"
}'



curl --request POST \
  --url http://envdev-api.x.me/user/api/user/register \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Accept-Language: zh-CN' \
  --header 'CLIENT-BUNDLE-ID: ' \
  --header 'CLIENT-DEVICE-ID: ' \
  --header 'CLIENT-DEVICE-MODEL: ' \
  --header 'CLIENT-PLATFORM-TYPE: ANDROID' \
  --header 'CLIENT-SYSTEM-VERSION: ' \
  --header 'CLIENT-TIMESTAMP: 1737095270' \
  --header 'CLIENT-VERSION: 1.0.61' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
    "email":"<EMAIL>",
    "username":"damoying",
    "emailCode":"226571",
    "password":"19930901",
    "inviteCode":"IMOI0",
    "responseToken":""
}'

curl --request POST \
  --url http://envdev-api.x.me/user/api/email/sendCode \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Accept-Language: zh-CN' \
  --header 'CLIENT-BUNDLE-ID: ' \
  --header 'CLIENT-DEVICE-ID: ' \
  --header 'CLIENT-DEVICE-MODEL: ' \
  --header 'CLIENT-PLATFORM-TYPE: ANDROID' \
  --header 'CLIENT-SYSTEM-VERSION: ' \
  --header 'CLIENT-TIMESTAMP: 1737095270' \
  --header 'CLIENT-VERSION: 1.0.61' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
	"email": "<EMAIL>",
	"businessType": "2"
}'


















curl --request POST \
  --url http://envdev-api.x.me/user/api/email/checkCode \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Accept-Language: zh-CN' \
  --header 'CLIENT-BUNDLE-ID: ' \
  --header 'CLIENT-DEVICE-ID: ' \
  --header 'CLIENT-DEVICE-MODEL: ' \
  --header 'CLIENT-PLATFORM-TYPE: ANDROID' \
  --header 'CLIENT-SYSTEM-VERSION: ' \
  --header 'CLIENT-TIMESTAMP: 1737095270' \
  --header 'CLIENT-VERSION: 1.0.61' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
    "uid":13110957800955,
	"email": "<EMAIL>",
	"businessType": "2",
    "code": 279387
}'




curl --request POST \
  --url http://envdev-api.x.me/user/internal/user/test/getSendCode \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Accept-Language: zh-CN' \
  --header 'CLIENT-BUNDLE-ID: ' \
  --header 'CLIENT-DEVICE-ID: ' \
  --header 'CLIENT-DEVICE-MODEL: ' \
  --header 'CLIENT-PLATFORM-TYPE: ANDROID' \
  --header 'CLIENT-SYSTEM-VERSION: ' \
  --header 'CLIENT-TIMESTAMP: 1737095270' \
  --header 'CLIENT-VERSION: 1.0.61' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
    "uid":13110957800955,
	"email": "<EMAIL>",
	"businessType": "2"
}'





curl --request POST \
  --url http:///envdev-api.x.me/user/internal/user/test/register \
  --header 'Accept: */*' \
  --header 'Accept-Encoding: gzip, deflate, br' \
  --header 'Accept-Language: zh-CN' \
  --header 'CLIENT-BUNDLE-ID: ' \
  --header 'CLIENT-DEVICE-ID: ' \
  --header 'CLIENT-DEVICE-MODEL: ' \
  --header 'CLIENT-PLATFORM-TYPE: ANDROID' \
  --header 'CLIENT-SYSTEM-VERSION: ' \
  --header 'CLIENT-TIMESTAMP: 1737095270' \
  --header 'CLIENT-VERSION: 1.0.61' \
  --header 'Connection: keep-alive' \
  --header 'Content-Type: application/json' \
  --header 'User-Agent: PostmanRuntime-ApipostRuntime/1.1.0' \
  --data '{
    "email":"<EMAIL>",
    "username":"damoying",
    "emailCode":"1",
    "password":"19930901",
    "inviteCode":"IMOI0",
    "responseToken":""
}'



<style type="text/css">.ql-size-small {font-size: 10px;}.ql-size-large {font-size: 18px;}.ql-size-huge {font-size: 32px;}p {line-height: 2px;}</style><p>Markets plunged after President Trump announced global tariffs, triggering the worst stock performances since 2020 and a sharp crypto pullback.</p><p>Institutional crypto adoption remains stalled due to regulatory uncertainty, leaving crypto markets largely driven by retail traders and hedge funds. However, analysts suggest Bitcoin may be evolving into a hedge against U.S. economic isolation and could rebound faster than other risk assets.</p><img src="https://s3.x.me/files/webp/20250407/05/007f0123b4115cc909f482212eb4abb0.webp" /><p>Tradfi markets have experienced a sharp downturn over the past two days following President Donald Trump's announcement of reciprocal worldwide tariffs, triggering a widespread selloff. The Dow, Nasdaq and S&P 500 had their worst single-day performances and overall weeks since the summer of 2020, reflecting broader risk-off sentiment.</p><p>Several Bitcoin mining stocks fell as much as 15% on Thursday, while spot Bitcoin ETFs saw nearly $100 million in net outflows. Altcoins have been hit harder than Bitcoin during the downturn, a trend Injective CEO Eric Chen attributes to Bitcoin’s evolving market structure post-ETF approval.</p><p>"Bitcoin is holding up better than altcoins because its market structure has fundamentally changed post-ETF, with demand now coming from retirement accounts, macro funds and corporate treasuries like MicroStrategy (MSTR) and GameStop (GME)," said Chen.</p><p>However, bitcoin and other cryptocurrencies began to rebound on Friday. The price of bitcoin was up 2.2% to $84,000, according to The Block's BTC price data. Among the 10 largest cryptos by market cap, ether (1.1%), XRP (4%), solana (6%) and dogecoin (7%) were also trading higher.</p><p>Pantera Capital General Partner Cosmo Jiang highlighted the macroeconomic nature of the current pullback, emphasizing that digital assets remain borderless and unaffected by tariffs in a direct sense.</p><p>"The tariff-driven pullback is idiosyncratic and not a reflection of deeper economic issues," Jiang noted. "Just like it was artificially injected in, it can also be taken out once the Trump administration feels it has won concessions. Digital assets, as the tip of the spear in growth assets, were the first to pull back and may also be the first to bottom out and rebound."</p><p>Standard Chartered analysts suggested bitcoin could be evolving into a hedge against U.S. economic isolation, making it more attractive to global investors looking for alternatives to traditional assets in times of geopolitical instability. This reinforces the argument that Bitcoin's growing demand may help it recover faster than other risk assets.</p><p>Institutional uncertainty and retail-led crypto market activity</p><p>The new policy imposes a baseline 10% tariff on imports, exempting USMCA-compliant goods. Some countries will see tariffs rise as high as 49%. The baseline 10% tariff will take effect on April 5, and the reciprocal tariffs are set to take effect on April 9. On Friday, Fed Chair Jerome Powell said the tariffs will likely raise inflation and slow U.S. economic growth.</p><p>Institutional investors remain hesitant to enter the crypto market meaningfully due to ongoing regulatory uncertainty, according to Benchmark equity analyst Mark Palmer.</p><p>"While there was a lot of excitement about institutional adoption of Bitcoin and crypto following the U.S. elections in November, the reality is that institutions are still looking for a green light to invest in the space in earnest," said Palmer. "They likely won’t have one until crypto market structure legislation is enacted and regulatory clarity is not only apparent but codified."</p><p>Without long-term institutional investors, Palmer crypto stocks remain largely in the hands of retail traders and hedge funds. Lawmakers are currently working on a crypto market structure bill, while President Trump has signaled he would like stablecoin legislation on his desk by August.</p><p>John Glover, chief investment officer at Ledn, said Trump’s tariff announcement wiped out significant equity wealth globally and pushed bitcoin down from the $88,000 level to the $81,500 area. However, from a technical analysis perspective, he suggests bitcoin remains in Wave IV of its Elliott Wave cycle — a theory that the natural ebb and flow of market psychology can be observed in trending markets – and is on the verge of its next potential rally.</p><p>"Unless we see a close below $62K, this wave count will play out as expected," said Glover, a former managing director at Barclays. "For those getting nervous, the options activity suggests many are hedging at $70K while allowing for upside participation up to $100K by the end of June."</p><p>Despite Bitcoin's relative resilience, experts warn that Trump's tariffs may derail crypto IPO plans, as heightened macroeconomic uncertainty could cause firms to delay their public listings. Given the reliance of IPOs on strong market conditions, several high-profile crypto firms considering going public may now rethink their timelines and wait for a more stable environment.</p><p>Disclaimer: The Block is an independent media outlet that delivers news, research, and data. As of November 2023, Foresight Ventures is a majority investor of The Block. Foresight Ventures invests in other companies in the crypto space. Crypto exchange Bitget is an anchor LP for Foresight Ventures. The Block continues to operate independently to deliver objective, impactful, and timely information about the crypto industry. Here are our current financial disclosures.</p><p>© 2025 The Block. All Rights Reserved. This article is provided for informational purposes only. It is not offered or intended to be used as legal, tax, investment, financial, or other advice.</p>





https://test-outer-webapp.x.me/details?contentId=2000134617850805376&tab=0?channel=appshare





{"level":"info","timestamp":"2025-04-09T03:36:11.658689265Z","caller":"http_services/geo.go:62","message":"geoResponse","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.65868646Z","geoResponse":{"code":200,"message":"success","success":true,"result":{"ip":"**************","country":"China","country_code":"CN"},"request_id":"8d22eb5d-aced-4358-befe-7da1d7eefb39"}}
{"level":"info","timestamp":"2025-04-09T03:36:11.658802934Z","caller":"handlers/utils.go:42","message":"Raw request body","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.658802404Z","body":"{\"page\":1,\"size\":10,\"tab\":2,\"version\":\"v2\"}"}
2025-04-09T03:36:11.659067993Z {"level":"info","timestamp":"2025-04-09T03:36:11.658821376Z","caller":"handlers/utils.go:46","message":"Raw request body","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.658821149Z","body":"{\"page\":1,\"size\":10,\"tab\":2,\"version\":\"v2\"}"}
2025-04-09T03:36:11.659079991Z {"level":"info","timestamp":"2025-04-09T03:36:11.658839981Z","caller":"handlers/utils.go:66","message":"Parsed request","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.658839037Z","keyword":"","page":1,"size":10}
2025-04-09T03:36:11.659084284Z {"level":"info","timestamp":"2025-04-09T03:36:11.658853819Z","caller":"handlers/content_handler.go:172","message":"Getting recommendations for userID","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.658853097Z","userID":0,"size":10,"offset":0,"lang":"zh_cn"}
{"level":"info","timestamp":"2025-04-09T03:36:11.658885955Z","caller":"http_services/gorse.go:53","message":"GetRecommendations request","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.658885653Z","url":"http://gorse-server.env-test.svc.cluster.local:8087/api/recommend/guest-216485205"}
{"level":"info","timestamp":"2025-04-09T03:36:11.705337014Z","caller":"handlers/content_handler.go:207","message":"Successfully retrieved recommendations","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.705333054Z","count":10}
2025-04-09T03:36:11.705625515Z {"level":"info","timestamp":"2025-04-09T03:36:11.705394846Z","caller":"handlers/content_handler.go:208","message":"Recommendations","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.705393822Z","size":10,"offset":0,"recommendations":[301741839258836,301741887175298,301741800993970,301741887015722,301741800993650,301741882313138,301741795901594,301741796118900,301741882759804,301741800931481]}
2025-04-09T03:36:11.705633798Z {"level":"info","timestamp":"2025-04-09T03:36:11.705411569Z","caller":"service/content_service.go:169","message":"Start getting recommended content details - Content count: %d, UserID: %d, Language: %s","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.705411119Z","count":10,"userID":0,"lang":"zh_cn"}
{"level":"info","timestamp":"2025-04-09T03:36:11.713453624Z","caller":"service/enrich.go:213","message":"Enriching content details for user ID: %d, content count: %d","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.71345062Z","userID":0,"count":10}
2025-04-09T03:36:11.713770023Z {"level":"info","timestamp":"2025-04-09T03:36:11.713524313Z","caller":"service/enrich.go:233","message":"Collected %d content IDs for enrichment","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.713523897Z","count":10}
{"level":"info","timestamp":"2025-04-09T03:36:11.713623348Z","caller":"http_services/user.go:139","message":"Getting author info for %d authors","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.713621277Z","count":18}
{"level":"info","timestamp":"2025-04-09T03:36:11.713593567Z","caller":"redis_service/content_action.go:242","message":"GetMultiContentCount called with contentIDs: %v","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.713593207Z","contentIDs":[301741839258836,301741887175298,301741800993970,301741887015722,301741800993650,301741882313138,301741795901594,301741796118900,301741882759804,301741800931481]}
{"level":"info","timestamp":"2025-04-09T03:36:11.713659417Z","caller":"http_services/user.go:167","message":"Request body prepared","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.713659065Z","body":{"currentUid":"","needFollowCount":false,"needFollowState":true,"uids":[14108757786329,14116757762945,16107994648154,13109191464252,16107985138510,16107985138510,11107965142720,11107965142720,19108089639076,14108797665529,11114671308567,11114671308567,15111810888161,11116555549987,11116555549987,11116544835666,17107988089782,16116739421301]}}
2025-04-09T03:36:11.713850171Z {"level":"info","timestamp":"2025-04-09T03:36:11.713666574Z","caller":"service/enrich.go:261","message":"Successfully loaded user Content status","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.713665036Z"}
{"level":"info","timestamp":"2025-04-09T03:36:11.714827768Z","caller":"redis_service/content_action.go:255","message":"GetMultiContentCount success","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.71482729Z","count":1}
2025-04-09T03:36:11.715119476Z {"level":"info","timestamp":"2025-04-09T03:36:11.714859474Z","caller":"redis_service/content_action.go:256","message":"contentCounts","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.71485916Z","contentCounts":{"301741882759804":{"author_id":11116544835666,"content_id":301741882759804,"read_count":1,"hot_count":2}}}
2025-04-09T03:36:11.715126427Z {"level":"info","timestamp":"2025-04-09T03:36:11.714884533Z","caller":"service/enrich.go:247","message":"Successfully loaded content counts for %d contents","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.714884313Z","count":1}
{"level":"info","timestamp":"2025-04-09T03:36:11.715187912Z","caller":"service/enrich.go:281","message":"Successfully get comment num","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.715186832Z"}
{"level":"info","timestamp":"2025-04-09T03:36:11.720848009Z","caller":"http_services/user.go:181","message":"Author response: %+v","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.720845439Z","authorResp":{"11107965142720":{"uid":11107965142720,"nickName":"先知","avatarUrl":"https://s3.x.me/images/202412/15/57430463c5e2ccf3fdd3821efce5f79e.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"11114671308567":{"uid":11114671308567,"nickName":"余烬","avatarUrl":"https://s3-video.x.me/x/img/2025-02-17/01/5da98833ff4945ae8b6a6bac8939f620.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"11116544835666":{"uid":11116544835666,"nickName":"Cloak M","avatarUrl":"https://s3-video.x.me/x/img/2025-03-06/20/b79319e7ab6e4433ac04c45d0ee1d23a.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"11116555549987":{"uid":11116555549987,"nickName":"以太坊老张.eth","avatarUrl":"https://s3-video.x.me/x/img/2025-03-06/20/********************************.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"13109191464252":{"uid":13109191464252,"nickName":"cvex_xyz","avatarUrl":"https://s3.x.me/avatars/Avatars00-36.png","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"14108757786329":{"uid":14108757786329,"nickName":"rick awsb ($people, $people)","avatarUrl":"https://s3.x.me/images/202412/23/e658b35a026583f59ecac657d966e92e.png","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"14108797665529":{"uid":14108797665529,"nickName":"Nillion","avatarUrl":"https://s3.x.me/images/202412/23/c3b9eb6ccfbfce46bfa56fc5bc71df84.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"14116757762945":{"uid":14116757762945,"nickName":"路人蝙","avatarUrl":"https://s3-video.x.me/x/img/2025-03-13/12/4fb6a5c63ad6423cb7394618ae45e6d3.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"15111810888161":{"uid":15111810888161,"nickName":"Yuyue 🦇🚢🐻
⛓️💹🧲","avatarUrl":"https://s3-video.x.me/x/img/2025-01-18/13/600a37a6c7744758930852452a2952cb.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"16107985138510":{"uid":16107985138510,"nickName":"小威🟥","avatarUrl":"https://s3.x.me/images/202412/15/63a2fe5844a3332b9ff8dec4f5bb0a3b.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"16107994648154":{"uid":16107994648154,"nickName":"陈小艺","avatarUrl":"https://s3.x.me/images/202412/15/5b631cd6c6bbf1b55c9f82dcdf4c535f.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"16116739421301":{"uid":16116739421301,"nickName":"Somnia - a Fully Onc","avatarUrl":"https://s3-video.x.me/x/img/2025-03-13/01/b194102a9841424fad2fde0b2328858b.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"17107988089782":{"uid":17107988089782,"nickName":"ZR蟾哥","avatarUrl":"https://s3.x.me/images/202412/15/4a83e7f7b9363b3c0a3e5f7b68ef1052.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"19108089639076":{"uid":19108089639076,"nickName":"孤鹤.hl","avatarUrl":"https://s3.x.me/images/202412/15/0aa5459d13b549896665883bdd41f733.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1}}}
2025-04-09T03:36:11.721103193Z {"level":"info","timestamp":"2025-04-09T03:36:11.720914927Z","caller":"http_services/user.go:182","message":"Author response length: %d","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.720914697Z","length":14}
2025-04-09T03:36:11.721111535Z {"level":"info","timestamp":"2025-04-09T03:36:11.720931636Z","caller":"http_services/user.go:188","message":"Successfully retrieved info for %d authors","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.720931504Z","count":14}
2025-04-09T03:36:11.721130669Z {"level":"info","timestamp":"2025-04-09T03:36:11.720938714Z","caller":"http_services/user.go:189","message":"User info result: %+v","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.720938582
Z","result":{"11107965142720":{"uid":11107965142720,"nickName":"先知","avatarUrl":"https://s3.x.me/images/202412/15/57430463c5e2ccf3fdd3821efce5f79e.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"11114671308567":{"uid":11114671308567,"nickName":"余烬","avatarUrl":"https://s3-video.x.me/x/img/2025-02-17/01/5da98833ff4945ae8b6a6bac8939f620.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"11116544835666":{"uid":11116544835666,"nickName":"Cloak M","avatarUrl":"https://s3-video.x.me/x/img/2025-03-06/20/b79319e7ab6e4433ac04c45d0ee1d23a.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"11116555549987":{"uid":11116555549987,"nickName":"以太坊老张.eth","avatarUrl":"https://s3-video.x.me/x/img/2025-03-06/20/********************************.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"13109191464252":{"uid":13109191464252,"nickName":"cvex_xyz","avatarUrl":"https://s3.x.me/avatars/Avatars00-36.png","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"14108757786329":{"uid":14108757786329,"nickName":"rick awsb ($people, $people)","avatarUrl":"https://s3.x.me/images/202412/23/e658b35a026583f59ecac657d966e92e.png","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"14108797665529":{"uid":14108797665529,"nickName":"Nillion","avatarUrl":"https://s3.x.me/images/202412/23/c3b9eb6ccfbfce46bfa56fc5bc71df84.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"14116757762945":{"uid":14116757762945,"nickName":"路人蝙","avatarUrl":"https://s3-video.x.me/x/img/2025-03-13/12/4fb6a5c63ad6423cb7394618ae45e6d3.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"15111810888161":{"uid":15111810888161,"nickName":"Yuyue 🦇🚢🐻⛓️💹🧲","avatarUrl":"https://s3-video.x.me/x/img/2025-01-18/13/600a37a6c7744758930852452a2952cb.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"16107985138510":{"uid":16107985138510,"nickName":"小威🟥","avatarUrl"
:"https://s3.x.me/images/202412/15/63a2fe5844a3332b9ff8dec4f5bb0a3b.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"16107994648154":{"uid":16107994648154,"nickName":"陈小艺","avatarUrl":"https://s3.x.me/images/202412/15/5b631cd6c6bbf1b55c9f82dcdf4c535f.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"16116739421301":{"uid":16116739421301,"nickName":"Somnia - a Fully Onc","avatarUrl":"https://s3-video.x.me/x/img/2025-03-13/01/b194102a9841424fad2fde0b2328858b.jpeg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"17107988089782":{"uid":17107988089782,"nickName":"ZR蟾哥","avatarUrl":"https://s3.x.me/images/202412/15/4a83e7f7b9363b3c0a3e5f7b68ef1052.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1},"19108089639076":{"uid":19108089639076,"nickName":"孤鹤.hl","avatarUrl":"https://s3.x.me/images/202412/15/0aa5459d13b549896665883bdd41f733.jpg","followState":0,"fansNumber":0,"genesisBadge":0,"virtualType":1}}}
2025-04-09T03:36:11.721140266Z {"level":"info","timestamp":"2025-04-09T03:36:11.720970999Z","caller":"service/enrich.go:270","message":"Successfully get author info","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.720970846Z"}
2025-04-09T03:36:11.721148706Z 2025-04-09T03:36:11.721Z	INFO	service/enrich.go:470	Success execute tasks
2025-04-09T03:36:11.721154757Z {"level":"info","timestamp":"2025-04-09T03:36:11.721031203Z","caller":"service/enrich.go:368","message":"Successfully enriched %d contents","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.721030992Z","count":10}
2025-04-09T03:36:11.721159755Z {"level":"info","timestamp":"2025-04-09T03:36:11.721052396Z","caller":"handlers/content_handler.go:215","message":"Successfully retrieved details for recommendations","env":"prod","service":"content-service","timestamp":"2025-04-09T03:36:11.721052256Z","count":0}




{
  "query": {
    "bool": {
      "must": {
        "exists": {
          "field": "media_list"
        }
      }
    }
  },
  "size": 10,
  "from": 0,
  "sort": []
}


media-user.env-test.svc.cluster.local


https://test-outer-webapp.x.me/details?contentId=801739768437152&tab=0?channel=appshare




docker exec -it kafka bash
/opt/kafka_2.13-2.8.1/bin/kafka-topics.sh --create \
--bootstrap-server localhost:9092 \
--replication-factor 1 \
--partitions 1 \
--topic test-topic






{
	"code": 200,
	"message": "Success",
	"success": true,
	"result": [{
		"content_id": "2000134814211999167",
		"title": "test 关注我关注的可以见",
		"sub_title": "",
		"hot_count": 0,
		"content": "",
		"content_type": "ugc",
		"author_id": 13118594698948,
		"author_name": "damoying123",
		"author_avatar": "https://s3.x.me/files/png/20250416/02/0559938a117c525df46dc0884feeb799.png",
		"author_genesis_badge": 0,
		"is_follow": 0,
		"tags": null,
		"read_count": 0,
		"like_count": 0,
		"collect_count": 0,
		"share_count": 0,
		"create_time": "",
		"media_list": [],
		"source": "ugc",
		"timestamp": 1744772103,
		"is_collected": false,
		"is_liked": false,
		"forward_id": 0,
		"forward_status": 0,
		"forward_data": null,
		"media_type": 1,
		"language": "zh_cn",
		"segments": [{
			"type": "text",
			"id": 0,
			"value": "test 关注我关注的可以见"
		}],
		"status": 0,
		"can_share": false,
		"visibility": 2,
		"is_pinned": 0,
		"is_del": 0,
		"control_status": 0,
		"user_id": 0,
		"type": "",
		"comment_count": 0
	}, {
		"content_id": "2000134814138713532",
		"title": "测试仅关注我可见\n123",
		"sub_title": "",
		"hot_count": 2,
		"content": "",
		"content_type": "ugc",
		"author_id": 13118594698948,
		"author_name": "damoying123",
		"author_avatar": "https://s3.x.me/files/png/20250416/02/0559938a117c525df46dc0884feeb799.png",
		"author_genesis_badge": 0,
		"is_follow": 0,
		"tags": null,
		"read_count": 1,
		"like_count": 0,
		"collect_count": 0,
		"share_count": 0,
		"create_time": "",
		"media_list": [],
		"source": "ugc",
		"timestamp": 1744771817,
		"is_collected": false,
		"is_liked": false,
		"forward_id": 0,
		"forward_status": 0,
		"forward_data": null,
		"media_type": 1,
		"language": "zh_cn",
		"segments": [{
			"type": "text",
			"id": 0,
			"value": "测试仅关注我可见\n123"
		}],
		"status": 0,
		"can_share": false,
		"visibility": 2,
		"is_pinned": 0,
		"is_del": 0,
		"control_status": 0,
		"user_id": 0,
		"type": "",
		"comment_count": 0
	}, {
		"content_id": "2000134775886055486",
		"title": "the test world\ngood",
		"sub_title": "",
		"hot_count": 2,
		"content": "",
		"content_type": "ugc",
		"author_id": 13118594698948,
		"author_name": "damoying123",
		"author_avatar": "https://s3.x.me/files/png/20250416/02/0559938a117c525df46dc0884feeb799.png",
		"author_genesis_badge": 0,
		"is_follow": 0,
		"tags": null,
		"read_count": 1,
		"like_count": 0,
		"collect_count": 0,
		"share_count": 0,
		"create_time": "",
		"media_list": [],
		"source": "ugc",
		"timestamp": 1744622392,
		"is_collected": false,
		"is_liked": false,
		"forward_id": 0,
		"forward_status": 0,
		"forward_data": null,
		"media_type": 1,
		"language": "zh_cn",
		"segments": [{
			"type": "text",
			"id": 0,
			"value": "the test world\ngood"
		}],
		"status": 0,
		"can_share": false,
		"visibility": 3,
		"is_pinned": 0,
		"is_del": 0,
		"control_status": 0,
		"user_id": 0,
		"type": "",
		"comment_count": 0
	}],
	"request_id": ""
}








docker pull elastcisearch:8.7.1



docker cp 5283ce598a39:/usr/share/elasticsearch/config/elasticsearch.yml /usr/local/share/elasticsearch/elasticsearch.yml


docker cp ./elasticsearch.yml 5283ce598a39:/usr/share/elasticsearch/config/elasticsearch.yml


"{\"code\":200,\"message\":\"success\",\"result\":{\"19109183312800\":{\"uid\":19109183312800,\"username\":null,\"countryCode\":null,\"phonePrefix\":null,\"phone\":null,\"emailVerify\":null,\"phoneVerify\":null,\"email\":\"<EMAIL>\",\"language\":\"zh-CN\",\"avatarUrl\":\"https://s3.x.me/files/jpg/20250308/07/c82a9fff623741a63adb23653d655324.jpg\",\"background\":\"https://s3.x.me/profile/background-3.png\",\"inviteCode\":\"UT2UK\",\"nickName\":\"recommend english kol test\",\"personIntroduce\":\"111\",\"status\":1,\"level\":0,\"followersNumber\":0,\"fansNumber...+290 more"



kols.banner.jump_type
kols.banner.jump_url
kols.banner.icon



{
	"going":{
		"jump_type":"h5",
		"jump_url":"test",
		"icon":"test"
	},
	"ending":{
		"jump_type":"h5",
		"jump_url":"test",
		"icon":"test"
	}
}




# 运行容器，映射 SSH 端口 2222->22，挂载代码目录（可选）
docker run -d \
    --name java-dev \
    -p 2222:22 \
    -v /Users/<USER>/Work/work_env/java:/home/<USER>
    java-dev-env:latest


export https_proxy=http://127.0.0.1:7890 http_proxy=http://127.0.0.1:7890 all_proxy=socks5://127.0.0.1:7890

docker run --hostname=5a872e0bf93e --mac-address=56:fe:6a:02:44:59 --env=PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin --volume=/Users/<USER>/Work/work_env/java:/home/<USER>'org.opencontainers.image.ref.name=ubuntu' --label='org.opencontainers.image.version=22.04' --runtime=runc -d java-dev-env:latest












content:comment:count:141741849610265
comment:set:141741849610265






docker run -d \
  --name ollama \
  -e OLLAMA_MODELS=/root/models \
  -v /Users/<USER>/Work/mine/langchain/ollama/data:/root/.ollama \
  -v /Users/<USER>/Work/mine/langchain/ollama/models:/root/models \
  -p 11434:11434 \
  ollama/ollama:latest




docker run -d -p 6333:6333 -p 6334:6334 \
-v /Users/<USER>/Work/mine/langchain/qdrant/storage:/qdrant/storage \
qdrant/qdrant:latest



18116122697840,
12119983323748,
16122950317858,
17122901713741,
14122829612287,
10123001837823,
19122891621527,
14122816828085,
14123028432424,
16122853874298,
10122991596472,
17122900469229,
16122902245847,
17122813957386,
18122880322011,
12122898795308,
18122934692342,
17122955455837,
11122885867953,
14107912448586,




GET auth:users:token:449ac2a4884147999f6832973209f434



GET /risk/ban/status

{
	"ugc": true
	"comment": true
	"ban_tag_msg": "该账号因违反《社区内容创作公约》已被永久禁言"
}




<img alt="avatar" class="MuiAvatar-img css-1hy9t21" src="https://s3-video.x.me/tt/img/2025-05-07/00/f13adcb27c8d43c29625ec5e50eb3574.jpeg">







"e93500eb507a4366b7012238a907c767"





"e93500eb507a4366b7012238a907c767"




"xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com:6379"
