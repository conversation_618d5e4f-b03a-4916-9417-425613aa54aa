{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.42816986Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a0f63a31-10ce-465d-ba07-7afb9c53ad5a",
    "timestamp": "2025-05-09T04:11:00.42816767Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.428214192Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a0f63a31-10ce-465d-ba07-7afb9c53ad5a",
    "timestamp": "2025-05-09T04:11:00.428213992Z",
    "body": {
        "currentUid": "17122301679999",
        "uids": [
            19122351614492
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.429038312Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0f9e6d0e-e3de-49c4-bbd2-8c1e1201d203",
    "timestamp": "2025-05-09T04:11:00.429036062Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.429079012Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0f9e6d0e-e3de-49c4-bbd2-8c1e1201d203",
    "timestamp": "2025-05-09T04:11:00.429078872Z",
    "body": {
        "currentUid": "17122301679999",
        "uids": [
            19122351614492
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.433845318Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a0f63a31-10ce-465d-ba07-7afb9c53ad5a",
    "timestamp": "2025-05-09T04:11:00.433844618Z",
    "authorResp": {
        "19122351614492": {
            "uid": 19122351614492,
            "nickName": "waleednawaz613",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 1,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.433874539Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a0f63a31-10ce-465d-ba07-7afb9c53ad5a",
    "timestamp": "2025-05-09T04:11:00.433874389Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.433885849Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a0f63a31-10ce-465d-ba07-7afb9c53ad5a",
    "timestamp": "2025-05-09T04:11:00.433885719Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.433892429Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a0f63a31-10ce-465d-ba07-7afb9c53ad5a",
    "timestamp": "2025-05-09T04:11:00.433892319Z",
    "result": {
        "19122351614492": {
            "uid": 19122351614492,
            "nickName": "waleednawaz613",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 1,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.435069716Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0f9e6d0e-e3de-49c4-bbd2-8c1e1201d203",
    "timestamp": "2025-05-09T04:11:00.435069136Z",
    "authorResp": {
        "19122351614492": {
            "uid": 19122351614492,
            "nickName": "waleednawaz613",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 1,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.435103518Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0f9e6d0e-e3de-49c4-bbd2-8c1e1201d203",
    "timestamp": "2025-05-09T04:11:00.435103358Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.435111658Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0f9e6d0e-e3de-49c4-bbd2-8c1e1201d203",
    "timestamp": "2025-05-09T04:11:00.435111548Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:00.435118698Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0f9e6d0e-e3de-49c4-bbd2-8c1e1201d203",
    "timestamp": "2025-05-09T04:11:00.435118588Z",
    "result": {
        "19122351614492": {
            "uid": 19122351614492,
            "nickName": "waleednawaz613",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 1,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:03.669885876Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "56ad190f-39fd-4382-979f-ab8ead2e0256",
    "timestamp": "2025-05-09T04:11:03.669883596Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:03.669943188Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "56ad190f-39fd-4382-979f-ab8ead2e0256",
    "timestamp": "2025-05-09T04:11:03.669942968Z",
    "body": {
        "currentUid": "17122301679999",
        "uids": [
            19122351614492
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:03.675123772Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "56ad190f-39fd-4382-979f-ab8ead2e0256",
    "timestamp": "2025-05-09T04:11:03.675123152Z",
    "authorResp": {
        "19122351614492": {
            "uid": 19122351614492,
            "nickName": "waleednawaz613",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 1,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:03.675158523Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "56ad190f-39fd-4382-979f-ab8ead2e0256",
    "timestamp": "2025-05-09T04:11:03.675158183Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:03.675167683Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "56ad190f-39fd-4382-979f-ab8ead2e0256",
    "timestamp": "2025-05-09T04:11:03.675167553Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:03.675174403Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "56ad190f-39fd-4382-979f-ab8ead2e0256",
    "timestamp": "2025-05-09T04:11:03.675174293Z",
    "result": {
        "19122351614492": {
            "uid": 19122351614492,
            "nickName": "waleednawaz613",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 1,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:05.614750315Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b646a493-5d88-40c3-9d4c-c7458e1479c2",
    "timestamp": "2025-05-09T04:11:05.614748585Z",
    "ip": "***************, ************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:10.556572839Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "211ca6cb-8151-41c8-8d92-efd8ca8b404d",
    "timestamp": "2025-05-09T04:11:10.556571389Z",
    "ip": "***********, **********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:17.69030857Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4b19545a-42f0-4857-ad17-e787b0155443",
    "timestamp": "2025-05-09T04:11:17.69030665Z",
    "ip": "*************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:21.283045593Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "55d06327-7068-4ff1-ab4f-d982e077f855",
    "timestamp": "2025-05-09T04:11:21.283044333Z",
    "ip": "*************, **********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:23.243861948Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dff31bf3-9ddc-405b-9584-b0f1c642d6ab",
    "timestamp": "2025-05-09T04:11:23.243860358Z",
    "ip": "*************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:24.06480378Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3b7d3a4c-302b-44cc-9dee-6ffb4167a559",
    "timestamp": "2025-05-09T04:11:24.06480329Z",
    "ip": "***********, ***********"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:24.066601971Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3b7d3a4c-302b-44cc-9dee-6ffb4167a559",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:24.06658811Z",
    "media_id": "708652340357517312",
    "s3_key": "images/prod/708652340357517312/f51288294673f9b3f3923bf583ad2d8f.jpg"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:28.820953912Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "timestamp": "2025-05-09T04:11:28.820952112Z",
    "ip": "***********, ***********"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:28.821904672Z",
    "caller": "content/posts_create.go:62",
    "message": "开始创建帖子",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:28.821903392Z",
    "user_id": 15121475718394,
    "content_length": 4,
    "media_count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:28.823050756Z",
    "caller": "material/material.go:50",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:28.823049966Z",
    "body": {
        "source": "ugc",
        "url": "404a363e3ea1ec171dc24c03436e3522"
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:28.823448874Z",
    "caller": "services/media_service.go:571",
    "message": "Retrieved media information from Redis using MGET",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:28.823448094Z",
    "requested": 1,
    "found": 1,
    "missing": 0
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:30.020728721Z",
    "caller": "material/material.go:64",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:30.020725571Z",
    "authorResp": 2000135324109310140
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:30.053679606Z",
    "caller": "services/content_sync_service.go:130",
    "message": "Successfully pushed UGC content to DynamoDB",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:30.053678186Z",
    "contentID": "2000135324109310140",
    "userID": 15121475718394
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:30.05581561Z",
    "caller": "content/posts_create.go:317",
    "message": "帖子创建成功",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:30.05581492Z",
    "user_id": 15121475718394,
    "content_length": 4,
    "media_count": 1,
    "post_id": 2000135324109310140,
    "duration": "1.233910238s"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:30.055855751Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "dad6b6dd-d628-4db6-a33c-523f6b792ba0",
    "user_id": 15121475718394,
    "timestamp": "2025-05-09T04:11:30.055854791Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:30.055928222Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:11:30.055927552Z",
    "body": {
        "currentUid": "0",
        "uids": [
            15121475718394
        ]
    }
}
{
    "level": "error",
    "timestamp": "2025-05-09T04:11:30.056042375Z",
    "caller": "client/utils.go:36",
    "message": "HTTP request failed",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:11:30.056041485Z",
    "url": "http://media-user.env-prod.svc.cluster.local:80/user/internal/user/mes",
    "duration": "83.153µs",
    "error": "the server closed connection before returning the first response byte. Make sure the server returns 'Connection: close' response header before closing the connection",
    "stacktrace": "github.com/XmePlatform/ugc-hub/internal/client.SendJSONPOSTRequest\n\t/var/lib/jenkins/workspace/env/prod/ugc-hub/internal/client/utils.go:36\ngithub.com/XmePlatform/ugc-hub/internal/client/user.(*Client).BatchGetUserInfo\n\t/var/lib/jenkins/workspace/env/prod/ugc-hub/internal/client/user/user.go:64\ngithub.com/XmePlatform/ugc-hub/internal/services/content.(*Service).performAsyncPostTasks.func2\n\t/var/lib/jenkins/workspace/env/prod/ugc-hub/internal/services/content/posts_create.go:465"
}
{
    "level": "error",
    "timestamp": "2025-05-09T04:11:30.056086285Z",
    "caller": "user/user.go:66",
    "message": "Failed to get user",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:11:30.056085975Z",
    "error": "the server closed connection before returning the first response byte. Make sure the server returns 'Connection: close' response header before closing the connection",
    "stacktrace": "github.com/XmePlatform/ugc-hub/internal/client/user.(*Client).BatchGetUserInfo\n\t/var/lib/jenkins/workspace/env/prod/ugc-hub/internal/client/user/user.go:66\ngithub.com/XmePlatform/ugc-hub/internal/services/content.(*Service).performAsyncPostTasks.func2\n\t/var/lib/jenkins/workspace/env/prod/ugc-hub/internal/services/content/posts_create.go:465"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:30.28108997Z",
    "caller": "content/posts_create.go:495",
    "message": "已向Kafka发送帖子创建消息",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:11:30.28108828Z",
    "topic": "prod-content-ugc-async",
    "post_id": 2000135324109310140
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.058937645Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4794d4f4-5a49-43d0-8640-c3d064850d00",
    "timestamp": "2025-05-09T04:11:34.058935075Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.058987185Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4794d4f4-5a49-43d0-8640-c3d064850d00",
    "timestamp": "2025-05-09T04:11:34.058986975Z",
    "body": {
        "currentUid": "10122433750746",
        "uids": [
            10122415188304
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.069516965Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4794d4f4-5a49-43d0-8640-c3d064850d00",
    "timestamp": "2025-05-09T04:11:34.069516194Z",
    "authorResp": {
        "10122415188304": {
            "uid": 10122415188304,
            "nickName": "sakib243329",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-0.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 3,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.069550715Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4794d4f4-5a49-43d0-8640-c3d064850d00",
    "timestamp": "2025-05-09T04:11:34.069550545Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.069559345Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4794d4f4-5a49-43d0-8640-c3d064850d00",
    "timestamp": "2025-05-09T04:11:34.069559225Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.069565735Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4794d4f4-5a49-43d0-8640-c3d064850d00",
    "timestamp": "2025-05-09T04:11:34.069565625Z",
    "result": {
        "10122415188304": {
            "uid": 10122415188304,
            "nickName": "sakib243329",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-0.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 3,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.073681381Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a4c6c1fc-ba88-4aab-9fed-482a63f1e340",
    "timestamp": "2025-05-09T04:11:34.073679261Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.073723652Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a4c6c1fc-ba88-4aab-9fed-482a63f1e340",
    "timestamp": "2025-05-09T04:11:34.073723502Z",
    "body": {
        "currentUid": "10122433750746",
        "uids": [
            10122415188304
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.077872417Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a4c6c1fc-ba88-4aab-9fed-482a63f1e340",
    "timestamp": "2025-05-09T04:11:34.077871377Z",
    "authorResp": {
        "10122415188304": {
            "uid": 10122415188304,
            "nickName": "sakib243329",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-0.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 3,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.077905869Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a4c6c1fc-ba88-4aab-9fed-482a63f1e340",
    "timestamp": "2025-05-09T04:11:34.077905719Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.077914669Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a4c6c1fc-ba88-4aab-9fed-482a63f1e340",
    "timestamp": "2025-05-09T04:11:34.077914559Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:11:34.077921339Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "a4c6c1fc-ba88-4aab-9fed-482a63f1e340",
    "timestamp": "2025-05-09T04:11:34.077921219Z",
    "result": {
        "10122415188304": {
            "uid": 10122415188304,
            "nickName": "sakib243329",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-0.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 3,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:11:40.208469238Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "5528891a-afee-4136-89ad-d966eb0376e5",
    "timestamp": "2025-05-09T04:11:40.208467468Z",
    "ip": "2a03:94e0:ffff:194:110:207:0:6, ***************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:05.776018916Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "aa0ec892-e483-4092-a9c8-c01b5c6cd50d",
    "timestamp": "2025-05-09T04:12:05.776017276Z",
    "ip": "2a03:94e0:ffff:194:110:207:0:6, ***************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.776504819Z",
    "caller": "content/posts_create.go:62",
    "message": "开始创建帖子",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "aa0ec892-e483-4092-a9c8-c01b5c6cd50d",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:12:05.776503709Z",
    "user_id": 18122447525199,
    "content_length": 47,
    "media_count": 0
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.777746404Z",
    "caller": "material/material.go:50",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "aa0ec892-e483-4092-a9c8-c01b5c6cd50d",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:12:05.777745684Z",
    "body": {
        "source": "ugc",
        "url": "43325e1ce2493f442a7d249d5164ebbe"
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.857972455Z",
    "caller": "material/material.go:64",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "aa0ec892-e483-4092-a9c8-c01b5c6cd50d",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:12:05.857971565Z",
    "authorResp": 2000135324118488240
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.887249969Z",
    "caller": "services/content_sync_service.go:130",
    "message": "Successfully pushed UGC content to DynamoDB",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "aa0ec892-e483-4092-a9c8-c01b5c6cd50d",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:12:05.887249079Z",
    "contentID": "2000135324118488240",
    "userID": 18122447525199
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.888815382Z",
    "caller": "content/posts_create.go:317",
    "message": "帖子创建成功",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "aa0ec892-e483-4092-a9c8-c01b5c6cd50d",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:12:05.888814622Z",
    "user_id": 18122447525199,
    "content_length": 47,
    "media_count": 0,
    "post_id": 2000135324118488240,
    "duration": "112.309413ms"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.888902345Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:12:05.888902135Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.888944036Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:12:05.888943426Z",
    "body": {
        "currentUid": "0",
        "uids": [
            18122447525199
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.901003311Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:12:05.901002121Z",
    "authorResp": {
        "18122447525199": {
            "uid": 18122447525199,
            "nickName": "twitter",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.901049674Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:12:05.901049414Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.901064134Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:12:05.901063884Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.901075894Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:12:05.901075684Z",
    "result": {
        "18122447525199": {
            "uid": 18122447525199,
            "nickName": "twitter",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:05.9099405Z",
    "caller": "content/posts_create.go:495",
    "message": "已向Kafka发送帖子创建消息",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:12:05.90993953Z",
    "topic": "prod-content-ugc-async",
    "post_id": 2000135324118488240
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:16.427713123Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "296c93b9-b44e-465b-9a14-72bc7c22c9fb",
    "timestamp": "2025-05-09T04:12:16.427711343Z",
    "ip": "2a03:94e0:ffff:194:110:207:0:6, ***************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:18.87619465Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "94a6ea17-5b35-4552-8519-0b1ec209e5da",
    "timestamp": "2025-05-09T04:12:18.87619308Z",
    "ip": "*************, *************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:12:18.877877994Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "94a6ea17-5b35-4552-8519-0b1ec209e5da",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:12:18.877876824Z",
    "media_id": "708652570255712256",
    "s3_key": "videos/prod/708652570255712256/ea8e56874cd05c1b270f6e1ad63761c9.mp4"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:28.639814957Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "204bde94-67c6-4a0b-b880-7c2cccc9c452",
    "timestamp": "2025-05-09T04:12:28.639813297Z",
    "ip": "**************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:29.006544534Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e8878c53-70ea-4b9e-a400-26dc11a4497e",
    "timestamp": "2025-05-09T04:12:29.006542514Z",
    "ip": "2a03:94e0:ffff:194:110:207:0:6, ***************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:29.987852204Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "47acf421-b786-4471-bb04-ad610981837f",
    "timestamp": "2025-05-09T04:12:29.987850744Z",
    "ip": "2a03:94e0:ffff:194:110:207:0:6, **************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:47.03044537Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "5e4177fa-812f-4c45-9d09-8f3eb84fab8e",
    "timestamp": "2025-05-09T04:12:47.03044323Z",
    "ip": "**************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:12:57.64754047Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "94c3a352-2616-4974-81c6-ffe856e8e31b",
    "timestamp": "2025-05-09T04:12:57.64753848Z",
    "ip": "*************, **************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:13:05.615149234Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c7506410-79a1-4d28-a7b1-12161776d125",
    "timestamp": "2025-05-09T04:13:05.615148724Z",
    "ip": "*************, *************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:13:27.929148298Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "9f4cfa60-9e68-427d-bd32-efeda35dd6b6",
    "timestamp": "2025-05-09T04:13:27.929146537Z",
    "ip": "*************, ************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:13:30.286254863Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "cd2a5858-cb14-4f3a-ab40-33a5e34cb489",
    "timestamp": "2025-05-09T04:13:30.286253253Z",
    "ip": "2a03:94e0:ffff:194:110:207:0:6, **************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:30.287515288Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "cd2a5858-cb14-4f3a-ab40-33a5e34cb489",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:30.287514138Z",
    "media_id": "708652869766762496",
    "s3_key": "images/prod/708652869766762496/63c7e73d8ea1ceb68e192750506a1aed.jpg"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:13:33.072495406Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "93ba4c67-c697-4006-8b0b-04f47633e991",
    "timestamp": "2025-05-09T04:13:33.072493836Z",
    "ip": "**************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:13:37.316476644Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "timestamp": "2025-05-09T04:13:37.316474064Z",
    "ip": "2a03:94e0:ffff:194:110:207:0:6, **************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.317460465Z",
    "caller": "content/posts_create.go:62",
    "message": "开始创建帖子",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.317459275Z",
    "user_id": 18122447525199,
    "content_length": 71,
    "media_count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.319212731Z",
    "caller": "material/material.go:50",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.319211641Z",
    "body": {
        "source": "ugc",
        "url": "c66188f417b569352414f54f3faf5d46"
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.32005017Z",
    "caller": "services/media_service.go:571",
    "message": "Retrieved media information from Redis using MGET",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.3200494Z",
    "requested": 1,
    "found": 1,
    "missing": 0
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.382381359Z",
    "caller": "material/material.go:64",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.382379519Z",
    "authorResp": 2000135324141919144
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.416446256Z",
    "caller": "services/content_sync_service.go:130",
    "message": "Successfully pushed UGC content to DynamoDB",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.416444616Z",
    "contentID": "2000135324141919144",
    "userID": 18122447525199
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.41904214Z",
    "caller": "content/posts_create.go:317",
    "message": "帖子创建成功",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.41904136Z",
    "user_id": 18122447525199,
    "content_length": 71,
    "media_count": 1,
    "post_id": 2000135324141919144,
    "duration": "101.582044ms"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.419088931Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.419087591Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.419135052Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c820f5a6-884a-41ad-b327-a027119150b0",
    "user_id": 18122447525199,
    "timestamp": "2025-05-09T04:13:37.419134302Z",
    "body": {
        "currentUid": "0",
        "uids": [
            18122447525199
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.430291817Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:13:37.430290697Z",
    "authorResp": {
        "18122447525199": {
            "uid": 18122447525199,
            "nickName": "twitter",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.430327407Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:13:37.430327237Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.430335327Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:13:37.430335217Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.430341567Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:13:37.430341457Z",
    "result": {
        "18122447525199": {
            "uid": 18122447525199,
            "nickName": "twitter",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:37.435726111Z",
    "caller": "content/posts_create.go:495",
    "message": "已向Kafka发送帖子创建消息",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:13:37.435724901Z",
    "topic": "prod-content-ugc-async",
    "post_id": 2000135324141919144
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:13:42.482014072Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b28dda04-629d-4bb9-9199-5102bacf9927",
    "timestamp": "2025-05-09T04:13:42.482012162Z",
    "ip": "**************, *************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.382372771Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "699881f4-8373-424c-820c-cc264341c33a",
    "timestamp": "2025-05-09T04:13:53.382370221Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.382414272Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "699881f4-8373-424c-820c-cc264341c33a",
    "timestamp": "2025-05-09T04:13:53.382414132Z",
    "body": {
        "currentUid": "12122454552293",
        "uids": [
            18121811536283
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.389588151Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "699881f4-8373-424c-820c-cc264341c33a",
    "timestamp": "2025-05-09T04:13:53.389587141Z",
    "authorResp": {
        "18121811536283": {
            "uid": 18121811536283,
            "nickName": "sazzad15",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-7.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 488,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.389628022Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "699881f4-8373-424c-820c-cc264341c33a",
    "timestamp": "2025-05-09T04:13:53.389627862Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.389636572Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "699881f4-8373-424c-820c-cc264341c33a",
    "timestamp": "2025-05-09T04:13:53.389636462Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.389643432Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "699881f4-8373-424c-820c-cc264341c33a",
    "timestamp": "2025-05-09T04:13:53.389643322Z",
    "result": {
        "18121811536283": {
            "uid": 18121811536283,
            "nickName": "sazzad15",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-7.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 488,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.390122764Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e8edf031-87fc-4188-8ff5-a7e9e11b46db",
    "timestamp": "2025-05-09T04:13:53.390120184Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.390183175Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e8edf031-87fc-4188-8ff5-a7e9e11b46db",
    "timestamp": "2025-05-09T04:13:53.390182935Z",
    "body": {
        "currentUid": "12122454552293",
        "uids": [
            18121811536283
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.39617359Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e8edf031-87fc-4188-8ff5-a7e9e11b46db",
    "timestamp": "2025-05-09T04:13:53.39617252Z",
    "authorResp": {
        "18121811536283": {
            "uid": 18121811536283,
            "nickName": "sazzad15",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-7.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 488,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.396210452Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e8edf031-87fc-4188-8ff5-a7e9e11b46db",
    "timestamp": "2025-05-09T04:13:53.396210292Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.396220822Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e8edf031-87fc-4188-8ff5-a7e9e11b46db",
    "timestamp": "2025-05-09T04:13:53.396220712Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:53.396227562Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e8edf031-87fc-4188-8ff5-a7e9e11b46db",
    "timestamp": "2025-05-09T04:13:53.396227452Z",
    "result": {
        "18121811536283": {
            "uid": 18121811536283,
            "nickName": "sazzad15",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-7.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 488,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.068898619Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "abdd8f14-41ef-4695-8bb1-c6146fe47a78",
    "timestamp": "2025-05-09T04:13:55.068898039Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.06895605Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "abdd8f14-41ef-4695-8bb1-c6146fe47a78",
    "timestamp": "2025-05-09T04:13:55.0689559Z",
    "body": {
        "currentUid": "15121919861978",
        "uids": [
            19121822992480
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.069321198Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0268fda8-6320-41a3-850b-0475acdaf0e2",
    "timestamp": "2025-05-09T04:13:55.069320588Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.069357469Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0268fda8-6320-41a3-850b-0475acdaf0e2",
    "timestamp": "2025-05-09T04:13:55.069357289Z",
    "body": {
        "currentUid": "15121919861978",
        "uids": [
            19121822992480
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.073649799Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "abdd8f14-41ef-4695-8bb1-c6146fe47a78",
    "timestamp": "2025-05-09T04:13:55.073649209Z",
    "authorResp": {
        "19121822992480": {
            "uid": 19121822992480,
            "nickName": "Real Sigma ",
            "avatarUrl": "https://s3.x.me/files/png/20250503/18/eaab313d74f31d4e5f7dfbd5fb3402b6.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 105,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.07368972Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "abdd8f14-41ef-4695-8bb1-c6146fe47a78",
    "timestamp": "2025-05-09T04:13:55.07368947Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.07370131Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "abdd8f14-41ef-4695-8bb1-c6146fe47a78",
    "timestamp": "2025-05-09T04:13:55.07370112Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.07371071Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "abdd8f14-41ef-4695-8bb1-c6146fe47a78",
    "timestamp": "2025-05-09T04:13:55.07371058Z",
    "result": {
        "19121822992480": {
            "uid": 19121822992480,
            "nickName": "Real Sigma ",
            "avatarUrl": "https://s3.x.me/files/png/20250503/18/eaab313d74f31d4e5f7dfbd5fb3402b6.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 105,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.07563Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0268fda8-6320-41a3-850b-0475acdaf0e2",
    "timestamp": "2025-05-09T04:13:55.07562943Z",
    "authorResp": {
        "19121822992480": {
            "uid": 19121822992480,
            "nickName": "Real Sigma ",
            "avatarUrl": "https://s3.x.me/files/png/20250503/18/eaab313d74f31d4e5f7dfbd5fb3402b6.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 105,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.075666611Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0268fda8-6320-41a3-850b-0475acdaf0e2",
    "timestamp": "2025-05-09T04:13:55.075666351Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.075678212Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0268fda8-6320-41a3-850b-0475acdaf0e2",
    "timestamp": "2025-05-09T04:13:55.075678002Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:13:55.075686912Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "0268fda8-6320-41a3-850b-0475acdaf0e2",
    "timestamp": "2025-05-09T04:13:55.075686752Z",
    "result": {
        "19121822992480": {
            "uid": 19121822992480,
            "nickName": "Real Sigma ",
            "avatarUrl": "https://s3.x.me/files/png/20250503/18/eaab313d74f31d4e5f7dfbd5fb3402b6.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 105,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:14:21.016817705Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "7ce7bd79-c152-4c4e-ab8c-8a2b6f357230",
    "timestamp": "2025-05-09T04:14:21.016816195Z",
    "ip": "**************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:14:26.328850133Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "9417f227-13bb-4eaf-949b-f8ea7f7b30e9",
    "timestamp": "2025-05-09T04:14:26.328848722Z",
    "ip": "**************, ***********"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:26.331156447Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "9417f227-13bb-4eaf-949b-f8ea7f7b30e9",
    "user_id": 13122072031800,
    "timestamp": "2025-05-09T04:14:26.331155187Z",
    "media_id": "708653104828141568",
    "s3_key": "images/prod/708653104828141568/7ce90cc26702d3aa85c910096d782b00.jpg"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:14:29.961977976Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "262109fe-54ae-40b5-83b1-02455f13965b",
    "timestamp": "2025-05-09T04:14:29.961976136Z",
    "ip": "*************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:14:30.285081284Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "302ec3f7-9440-49a8-80dd-1b8b1dc8862e",
    "timestamp": "2025-05-09T04:14:30.285079784Z",
    "ip": "**************, ***********"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.285483803Z",
    "caller": "content/posts_create.go:62",
    "message": "开始创建帖子",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "302ec3f7-9440-49a8-80dd-1b8b1dc8862e",
    "user_id": 13122072031800,
    "timestamp": "2025-05-09T04:14:30.285482263Z",
    "user_id": 13122072031800,
    "content_length": 15,
    "media_count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.286213487Z",
    "caller": "material/material.go:50",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "302ec3f7-9440-49a8-80dd-1b8b1dc8862e",
    "user_id": 13122072031800,
    "timestamp": "2025-05-09T04:14:30.286209837Z",
    "body": {
        "source": "ugc",
        "url": "e893ed6760aac2f9c589d4b52a9e3134"
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.286596775Z",
    "caller": "services/media_service.go:571",
    "message": "Retrieved media information from Redis using MGET",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "302ec3f7-9440-49a8-80dd-1b8b1dc8862e",
    "user_id": 13122072031800,
    "timestamp": "2025-05-09T04:14:30.286595865Z",
    "requested": 1,
    "found": 1,
    "missing": 0
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.360591281Z",
    "caller": "material/material.go:64",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "302ec3f7-9440-49a8-80dd-1b8b1dc8862e",
    "user_id": 13122072031800,
    "timestamp": "2025-05-09T04:14:30.360588861Z",
    "authorResp": 2000135324155480492
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.369915546Z",
    "caller": "services/content_sync_service.go:130",
    "message": "Successfully pushed UGC content to DynamoDB",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "302ec3f7-9440-49a8-80dd-1b8b1dc8862e",
    "user_id": 13122072031800,
    "timestamp": "2025-05-09T04:14:30.369914276Z",
    "contentID": "2000135324155480492",
    "userID": 13122072031800
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.372350467Z",
    "caller": "content/posts_create.go:317",
    "message": "帖子创建成功",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "302ec3f7-9440-49a8-80dd-1b8b1dc8862e",
    "user_id": 13122072031800,
    "timestamp": "2025-05-09T04:14:30.372349407Z",
    "user_id": 13122072031800,
    "content_length": 15,
    "media_count": 1,
    "post_id": 2000135324155480492,
    "duration": "86.865764ms"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.372446129Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:14:30.372445659Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.3724849Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:14:30.37248465Z",
    "body": {
        "currentUid": "0",
        "uids": [
            13122072031800
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.377605776Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:14:30.377605196Z",
    "authorResp": {
        "13122072031800": {
            "uid": 13122072031800,
            "nickName": "litonhasan121",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 7,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.377633147Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:14:30.377632957Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.377641177Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:14:30.377641067Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.377647808Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:14:30.377647698Z",
    "result": {
        "13122072031800": {
            "uid": 13122072031800,
            "nickName": "litonhasan121",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-5.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 7,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:30.381288544Z",
    "caller": "content/posts_create.go:495",
    "message": "已向Kafka发送帖子创建消息",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:14:30.381287984Z",
    "topic": "prod-content-ugc-async",
    "post_id": 2000135324155480492
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:14:56.060854637Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "8048af3e-07ff-4e15-ab60-200ea705fd3e",
    "timestamp": "2025-05-09T04:14:56.060852667Z",
    "ip": "*************, *************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:14:56.062610283Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "8048af3e-07ff-4e15-ab60-200ea705fd3e",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:14:56.062609113Z",
    "media_id": "708653229533192192",
    "s3_key": "videos/prod/708653229533192192/ea8e56874cd05c1b270f6e1ad63761c9.mp4"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:16.378436472Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "c04ff28c-1e27-4835-a658-66232a822b57",
    "timestamp": "2025-05-09T04:15:16.378434831Z",
    "ip": "**************, ************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:17.981979032Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "1ea8222b-0221-4b98-bdd7-1afd870d8bd2",
    "timestamp": "2025-05-09T04:15:17.981977472Z",
    "ip": "**************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:22.795230396Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3510dc38-f88d-4774-a232-3da8d2e332a9",
    "timestamp": "2025-05-09T04:15:22.795228686Z",
    "ip": "**************, ************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.796173748Z",
    "caller": "content/posts_create.go:62",
    "message": "开始创建帖子",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3510dc38-f88d-4774-a232-3da8d2e332a9",
    "user_id": 10122412296941,
    "timestamp": "2025-05-09T04:15:22.796172418Z",
    "user_id": 10122412296941,
    "content_length": 15,
    "media_count": 0
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.796863554Z",
    "caller": "material/material.go:50",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3510dc38-f88d-4774-a232-3da8d2e332a9",
    "user_id": 10122412296941,
    "timestamp": "2025-05-09T04:15:22.796862824Z",
    "body": {
        "source": "ugc",
        "url": "9f9351fb99e39a8f4fd561b17015319e"
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.859289131Z",
    "caller": "material/material.go:64",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3510dc38-f88d-4774-a232-3da8d2e332a9",
    "user_id": 10122412296941,
    "timestamp": "2025-05-09T04:15:22.859287931Z",
    "authorResp": 2000135324168921766
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.867700843Z",
    "caller": "services/content_sync_service.go:130",
    "message": "Successfully pushed UGC content to DynamoDB",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3510dc38-f88d-4774-a232-3da8d2e332a9",
    "user_id": 10122412296941,
    "timestamp": "2025-05-09T04:15:22.867698803Z",
    "contentID": "2000135324168921766",
    "userID": 10122412296941
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.870065337Z",
    "caller": "content/posts_create.go:317",
    "message": "帖子创建成功",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3510dc38-f88d-4774-a232-3da8d2e332a9",
    "user_id": 10122412296941,
    "timestamp": "2025-05-09T04:15:22.870064567Z",
    "user_id": 10122412296941,
    "content_length": 15,
    "media_count": 0,
    "post_id": 2000135324168921766,
    "duration": "73.891139ms"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.870142029Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3510dc38-f88d-4774-a232-3da8d2e332a9",
    "user_id": 10122412296941,
    "timestamp": "2025-05-09T04:15:22.870140909Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.87018317Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:15:22.87018292Z",
    "body": {
        "currentUid": "0",
        "uids": [
            10122412296941
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.881424097Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:15:22.881423037Z",
    "authorResp": {
        "10122412296941": {
            "uid": 10122412296941,
            "nickName": "zameer55",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-3.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.881460458Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:15:22.881460248Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.881468458Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:15:22.881468338Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:22.881475068Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:15:22.881474958Z",
    "result": {
        "10122412296941": {
            "uid": 10122412296941,
            "nickName": "zameer55",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-3.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:23.065595476Z",
    "caller": "content/posts_create.go:495",
    "message": "已向Kafka发送帖子创建消息",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:15:23.065593826Z",
    "topic": "prod-content-ugc-async",
    "post_id": 2000135324168921766
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:25.766453236Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b14ef9c2-41c8-4b02-8f6a-4a3079c6935e",
    "timestamp": "2025-05-09T04:15:25.766451226Z",
    "ip": "**************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:47.927502123Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "3182d53f-eb9d-4b6f-9652-00d477714d39",
    "timestamp": "2025-05-09T04:15:47.927500473Z",
    "ip": "**************, **************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:49.019093759Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "06b94e97-fe69-4547-a71d-7c3f4e27a040",
    "timestamp": "2025-05-09T04:15:49.019092309Z",
    "ip": "**************, ***********"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:15:49.020862958Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "06b94e97-fe69-4547-a71d-7c3f4e27a040",
    "user_id": 18121984306794,
    "timestamp": "2025-05-09T04:15:49.020852087Z",
    "media_id": "708653451655139328",
    "s3_key": "videos/prod/708653451655139328/ee0efb559b35301b64747973dc5ef2b5.mp4"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:50.63468837Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "55960f67-3c13-444c-a9e6-8f7892f5493f",
    "timestamp": "2025-05-09T04:15:50.63468652Z",
    "ip": "************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:15:55.243143616Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e81a9623-5a56-485a-bd85-b32db364bf18",
    "timestamp": "2025-05-09T04:15:55.243142096Z",
    "ip": "*************, *************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:18.430129401Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "444c9309-7aa9-4264-b4ae-c7e96318b159",
    "timestamp": "2025-05-09T04:16:18.430127651Z",
    "ip": "*************, *************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:18.431717159Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "444c9309-7aa9-4264-b4ae-c7e96318b159",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:18.431716269Z",
    "media_id": "708653575018012672",
    "s3_key": "videos/prod/708653575018012672/ea8e56874cd05c1b270f6e1ad63761c9.mp4"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.399621096Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "ed95d323-50cf-4d03-939f-472e5d382787",
    "timestamp": "2025-05-09T04:16:25.399618776Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.399672178Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "ed95d323-50cf-4d03-939f-472e5d382787",
    "timestamp": "2025-05-09T04:16:25.399672008Z",
    "body": {
        "currentUid": "16122385594516",
        "uids": [
            19121958274025
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.40949246Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "ed95d323-50cf-4d03-939f-472e5d382787",
    "timestamp": "2025-05-09T04:16:25.40949053Z",
    "authorResp": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.409556712Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "ed95d323-50cf-4d03-939f-472e5d382787",
    "timestamp": "2025-05-09T04:16:25.409556482Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.409568673Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "ed95d323-50cf-4d03-939f-472e5d382787",
    "timestamp": "2025-05-09T04:16:25.409568523Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.409577413Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "ed95d323-50cf-4d03-939f-472e5d382787",
    "timestamp": "2025-05-09T04:16:25.409577273Z",
    "result": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.435199615Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "64419a6a-ae20-4ae9-b40f-1ed0f7ce6777",
    "timestamp": "2025-05-09T04:16:25.435197465Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.435245056Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "64419a6a-ae20-4ae9-b40f-1ed0f7ce6777",
    "timestamp": "2025-05-09T04:16:25.435244906Z",
    "body": {
        "currentUid": "16122385594516",
        "uids": [
            19121958274025
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.440065471Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "64419a6a-ae20-4ae9-b40f-1ed0f7ce6777",
    "timestamp": "2025-05-09T04:16:25.440063811Z",
    "authorResp": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.440119282Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "64419a6a-ae20-4ae9-b40f-1ed0f7ce6777",
    "timestamp": "2025-05-09T04:16:25.440119132Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.440129632Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "64419a6a-ae20-4ae9-b40f-1ed0f7ce6777",
    "timestamp": "2025-05-09T04:16:25.440129522Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:25.440136612Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "64419a6a-ae20-4ae9-b40f-1ed0f7ce6777",
    "timestamp": "2025-05-09T04:16:25.440136512Z",
    "result": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:26.455197341Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "74579609-7a7f-4d37-b2a5-0bb8db6e9c8f",
    "timestamp": "2025-05-09T04:16:26.45518611Z",
    "ip": "**************, ************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:28.218705561Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "e59e543a-6484-425d-9893-fcceacad5486",
    "timestamp": "2025-05-09T04:16:28.218703891Z",
    "ip": "*************, ************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.10376464Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "da598da8-ecf0-40cb-8302-72d5566bbc02",
    "timestamp": "2025-05-09T04:16:29.10376377Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.103803952Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "da598da8-ecf0-40cb-8302-72d5566bbc02",
    "timestamp": "2025-05-09T04:16:29.103803802Z",
    "body": {
        "currentUid": "16122385594516",
        "uids": [
            19121958274025
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.106164645Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b2e4ba84-6bf4-44d9-a8b2-6a097feff0ec",
    "timestamp": "2025-05-09T04:16:29.106163845Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.106200357Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b2e4ba84-6bf4-44d9-a8b2-6a097feff0ec",
    "timestamp": "2025-05-09T04:16:29.106199947Z",
    "body": {
        "currentUid": "16122385594516",
        "uids": [
            19121958274025
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.110333561Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b2e4ba84-6bf4-44d9-a8b2-6a097feff0ec",
    "timestamp": "2025-05-09T04:16:29.110332721Z",
    "authorResp": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.110370811Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b2e4ba84-6bf4-44d9-a8b2-6a097feff0ec",
    "timestamp": "2025-05-09T04:16:29.110370571Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.110380983Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b2e4ba84-6bf4-44d9-a8b2-6a097feff0ec",
    "timestamp": "2025-05-09T04:16:29.110380863Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.110389463Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b2e4ba84-6bf4-44d9-a8b2-6a097feff0ec",
    "timestamp": "2025-05-09T04:16:29.110389323Z",
    "result": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.114120328Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "da598da8-ecf0-40cb-8302-72d5566bbc02",
    "timestamp": "2025-05-09T04:16:29.114119728Z",
    "authorResp": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.114151368Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "da598da8-ecf0-40cb-8302-72d5566bbc02",
    "timestamp": "2025-05-09T04:16:29.114151168Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.114159559Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "da598da8-ecf0-40cb-8302-72d5566bbc02",
    "timestamp": "2025-05-09T04:16:29.114159449Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:29.114166089Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "da598da8-ecf0-40cb-8302-72d5566bbc02",
    "timestamp": "2025-05-09T04:16:29.114165979Z",
    "result": {
        "19121958274025": {
            "uid": 19121958274025,
            "nickName": "sukice62",
            "avatarUrl": "https://s3.x.me/files/png/20250504/15/b4bec65fe904f41ec96b56164cd1116c.png",
            "followState": 1,
            "followedState": 0,
            "fansNumber": 45,
            "genesisBadge": 1,
            "virtualType": 0
        }
    }
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:30.136285585Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "5cb0c27f-a826-4036-bc75-3eeb6845830d",
    "timestamp": "2025-05-09T04:16:30.136283125Z",
    "ip": "**************, **************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:30.137472293Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "5cb0c27f-a826-4036-bc75-3eeb6845830d",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:30.137471313Z",
    "media_id": "708653624112340992",
    "s3_key": "images/prod/708653624112340992/97bd833fa371134a250f3a972403647a.jpg"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:30.138005965Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "5cb0c27f-a826-4036-bc75-3eeb6845830d",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:30.138004945Z",
    "media_id": "708653624116535296",
    "s3_key": "images/prod/708653624116535296/42af30425012b93834201415715160cd.jpg"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:30.259217339Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "5de553d4-6020-4f5a-9789-a0ed36f32e98",
    "timestamp": "2025-05-09T04:16:30.259215309Z",
    "ip": "**************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:34.098015749Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "timestamp": "2025-05-09T04:16:34.098014679Z",
    "ip": "**************, **************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.099037057Z",
    "caller": "content/posts_create.go:62",
    "message": "开始创建帖子",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.099036227Z",
    "user_id": 13122494541954,
    "content_length": 31,
    "media_count": 2
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.100779265Z",
    "caller": "material/material.go:50",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.100778435Z",
    "body": {
        "source": "ugc",
        "url": "f525eade0f2f3bca9109ed9f30ca9fb7"
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.101699661Z",
    "caller": "services/media_service.go:571",
    "message": "Retrieved media information from Redis using MGET",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.101698911Z",
    "requested": 2,
    "found": 2,
    "missing": 0
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.167293275Z",
    "caller": "material/material.go:64",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.167292435Z",
    "authorResp": 2000135324187176358
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.194619995Z",
    "caller": "services/content_sync_service.go:130",
    "message": "Successfully pushed UGC content to DynamoDB",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.194618995Z",
    "contentID": "2000135324187176358",
    "userID": 13122494541954
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.196726763Z",
    "caller": "content/posts_create.go:317",
    "message": "帖子创建成功",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.196725253Z",
    "user_id": 13122494541954,
    "content_length": 31,
    "media_count": 2,
    "post_id": 2000135324187176358,
    "duration": "97.687366ms"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.196766675Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.196765555Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.196793615Z",
    "caller": "user/user.go:62",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "30eb7b31-fec7-4fe3-a5d4-94fb2f4b1161",
    "user_id": 13122494541954,
    "timestamp": "2025-05-09T04:16:34.196793135Z",
    "body": {
        "currentUid": "0",
        "uids": [
            13122494541954
        ]
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.200974951Z",
    "caller": "user/user.go:76",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:16:34.200974411Z",
    "authorResp": {
        "13122494541954": {
            "uid": 13122494541954,
            "nickName": "WY125",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-1.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.201002092Z",
    "caller": "user/user.go:77",
    "message": "Author response length: %d",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:16:34.201001922Z",
    "length": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.201012082Z",
    "caller": "user/user.go:83",
    "message": "Successfully retrieved info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:16:34.201011922Z",
    "count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.201020422Z",
    "caller": "user/user.go:84",
    "message": "User info result: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:16:34.201020282Z",
    "result": {
        "13122494541954": {
            "uid": 13122494541954,
            "nickName": "WY125",
            "avatarUrl": "https://s3.x.me/avatars/Avatars00-1.png",
            "followState": 0,
            "followedState": 0,
            "fansNumber": 0,
            "genesisBadge": 0,
            "virtualType": 0
        }
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:34.209494018Z",
    "caller": "content/posts_create.go:495",
    "message": "已向Kafka发送帖子创建消息",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:16:34.209492458Z",
    "topic": "prod-content-ugc-async",
    "post_id": 2000135324187176358
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:56.309856321Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "4640254c-bf84-46c0-b3d0-a4db3288b36d",
    "timestamp": "2025-05-09T04:16:56.30985474Z",
    "ip": "***************, ***********"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:56.411790507Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "7aeaaead-f0f4-4e55-9868-d7393f1f93b8",
    "timestamp": "2025-05-09T04:16:56.411788627Z",
    "ip": "*************, *************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:56.413358184Z",
    "caller": "services/media_service.go:199",
    "message": "Media info stored in Redis",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "7aeaaead-f0f4-4e55-9868-d7393f1f93b8",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:56.413356894Z",
    "media_id": "708653734321872896",
    "s3_key": "images/prod/708653734321872896/b63f5e926b23f7aacbddbd228f366fa2.jpg"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:56.840134935Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "7991ae6b-1d46-447f-a47a-9b1be393bd76",
    "timestamp": "2025-05-09T04:16:56.840133415Z",
    "ip": "*************, ************"
}
{
    "level": "warn",
    "timestamp": "2025-05-09T04:16:57.786194751Z",
    "caller": "middleware/client_ip.go:30",
    "message": "无效的客户端IP地址",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b6c6d6e4-eb79-4fac-93bf-1906e05e64ca",
    "timestamp": "2025-05-09T04:16:57.786192801Z",
    "ip": "*************, ************"
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:57.786633023Z",
    "caller": "content/posts_create.go:62",
    "message": "开始创建帖子",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b6c6d6e4-eb79-4fac-93bf-1906e05e64ca",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:57.786631723Z",
    "user_id": 19122390454751,
    "content_length": 11,
    "media_count": 1
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:57.788396182Z",
    "caller": "material/material.go:50",
    "message": "Request body prepared",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b6c6d6e4-eb79-4fac-93bf-1906e05e64ca",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:57.788395092Z",
    "body": {
        "source": "ugc",
        "url": "71ae9c9ce93417e7eed94c650f0a1d71"
    }
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:57.789364369Z",
    "caller": "services/media_service.go:571",
    "message": "Retrieved media information from Redis using MGET",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b6c6d6e4-eb79-4fac-93bf-1906e05e64ca",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:57.789363619Z",
    "requested": 1,
    "found": 1,
    "missing": 0
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:57.879482624Z",
    "caller": "material/material.go:64",
    "message": "Author response: %+v",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b6c6d6e4-eb79-4fac-93bf-1906e05e64ca",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:57.879480254Z",
    "authorResp": 2000135324193246908
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:57.887436165Z",
    "caller": "services/content_sync_service.go:130",
    "message": "Successfully pushed UGC content to DynamoDB",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b6c6d6e4-eb79-4fac-93bf-1906e05e64ca",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:57.887412325Z",
    "contentID": "2000135324193246908",
    "userID": 19122390454751
}
{
    "level": "info",
    "timestamp": "2025-05-09T04:16:57.889410831Z",
    "caller": "content/posts_create.go:317",
    "message": "帖子创建成功",
    "env": "prod",
    "service": "ugc-hub",
    "request_id": "b6c6d6e4-eb79-4fac-93bf-1906e05e64ca",
    "user_id": 19122390454751,
    "timestamp": "2025-05-09T04:16:57.889409771Z",
    "user_id": 19122390454751,
    "content_length": 11,
    "media_count": 1,
    "post_id": 2000135324193246908,
    "duration": "102.774868ms"
}



{
    "level": "info",
    "timestamp": "2025-05-09T04:16:57.889531523Z",
    "caller": "user/user.go:51",
    "message": "Getting author info for %d authors",
    "env": "prod",
    "service": "ugc-hub",
    "timestamp": "2025-05-09T04:16:57.889531063Z",
    "count": 1
}